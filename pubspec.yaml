name: fstore
publish_to: "none"
description: Mobile commerce app by Flutter

# Release version: v5.3.0 - Flutter 3.32.0 (Do not change this line)
# A version number is three numbers separated by dots, like 1.2.43 - followed by an optional build number separated by a +.
# In Android, build-name is used as versionName while build-number used as versionCode.Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion. Read more about iOS versioning at https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+3000

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  inspireui: 2.3.0
  flux_ui: 0.0.8

  flux_localization:
    path: packages/flux_localization
  flux_interface:
    path: packages/flux_interface/
  flux_firebase:
    path: packages/flux_firebase/

  # Base
  intl: any
  crypto: 3.0.6
  path_provider: ^2.1.5
  url_launcher: 6.3.1
  flutter_secure_storage: ^9.2.4
  hive: 2.2.3
  hive_flutter: 1.1.0
  http: ^1.2.2
  dio: ^5.7.0
  http_auth: 1.0.4
  share_plus: 10.1.4
  tuple: 2.0.2
  collection: any
  quiver: 3.2.2
  async: any
  html: 0.15.5
  vector_math: 2.1.4
  path: any
  flutter_svg: ^2.0.13
  flutter_local_notifications: 18.0.1
  new_version_plus: 0.0.11

  # STATE MANAGEMENT
  provider: 6.1.2

  # DEPENDENCY INJECTION
  get_it: ^8.0.2
  injectable: ^2.5.0

  # SPLASH SCREEN, ONBOARD
  lottie: ^3.1.3
  rive: ^0.13.15

  # WEB TOOLS
  html_unescape: 2.0.0
  webview_flutter: 4.9.0
  webview_flutter_web: 0.2.3+3
  responsive_builder: 0.7.1
  flutter_inappwebview: 6.1.1
  # Fix issue with flutter_inappwebview (Blank screen on Android)
  webview_flutter_android: 3.16.7

  # HTML render
  flutter_widget_from_html_core: ^0.15.2
  fwfh_svg: 0.8.3
  fwfh_cached_network_image: 0.14.3
  fwfh_url_launcher: 0.9.1
  fwfh_chewie: 0.14.8
  fwfh_webview: 0.15.2

  # VIDEO
  video_player: ^2.9.2
  youtube_player_iframe:
    git:
      url: https://github.com/inspireui/youtube_player_flutter.git
      path: packages/youtube_player_iframe
      ref: c2268281ca7f1f051f7ccab373e982d22c540910
  get_thumbnail_video:
    git:
      url: https://github.com/inspireui/video_thumbnail
      ref: master

  # MAP
  google_maps_flutter: 2.10.0
  # constraints for google_maps_flutter
  google_maps_flutter_android: ^2.14.11
  google_maps_flutter_ios: 2.13.1
  location: ^8.0.0
  geocoding: ^3.0.0

  # AUTHENTICATION
  local_auth: 2.3.0
  the_apple_sign_in: 1.1.1
  flutter_facebook_auth: 7.1.2
  google_sign_in: 6.2.2
  permission_handler: ^11.3.1
  flutter_web_auth_2: 4.1.0

  # FILES, IMAGES
  file_picker: 8.1.3
  cached_network_image: 3.4.1
  image: ^4.3.0
  transparent_image: 2.0.1
  image_picker: ^1.1.2
  flutter_cache_manager: ^3.4.1

  # TOOLS
  google_fonts: ^6.2.1
  random_string: 2.3.1
  json_annotation: ^4.9.0
  freezed_annotation: 2.4.4
  timeago: 3.7.1
  universal_platform: ^1.1.0
  uuid: ^4.5.1
  easy_debounce: 2.0.3
  devicelocale: ^0.8.1
  visibility_detector: 0.4.0+2
  rate_my_app: ^2.2.0
  flutter_linkify: 6.0.0
  gms_check: 1.0.3
  google_mobile_ads: ^5.2.0
  speech_to_text: 7.0.0
  flutter_keyboard_visibility: 6.0.0
  app_tracking_transparency: ^2.0.6
  flutter_branch_sdk: 8.2.0
  facebook_app_events: 0.19.7

  # UI
  flutter_spinkit: ^5.2.1
  smooth_page_indicator: 1.2.0+3
  animated_text_kit: 4.2.3
  flutter_staggered_grid_view: 0.7.0
  pin_code_fields: 8.0.1
  country_pickers: 3.0.1
  dropdown_search: 5.0.6
  intro_slider: 4.2.1
  extended_image: 10.0.1
  dotted_decoration: 2.0.0
  awesome_card: 1.1.7
  qr_code_scanner: 1.0.1
  sticky_headers: 0.3.0+2
  jumping_dot: 0.0.7
  fl_chart: ^0.69.0
  tiktoklikescroller: 0.2.8
  readmore: ^3.0.0
  flutter_animate: 4.5.2
  scrollable_positioned_list: 0.3.8
  country_code_picker: 3.2.0
  carousel_slider_plus: 7.1.0
  rect_getter: 1.1.0
  scroll_to_index: 3.0.1
  flutter_zoom_drawer: 3.2.0
  infinite_carousel: 1.1.1
  timezone: 0.10.0
  sliver_tools: ^0.2.12
  flutter_staggered_animations: 1.1.1
  flutter_lazy_indexed_stack: 0.0.6
  bot_toast: ^4.1.3
  archive: ^3.6.1

  ### Native Payment
  phonepe_payment_sdk: ^2.0.3
  razorpay_flutter:
    git:
      url: https://github.com/inspireui/razorpay-flutter.git
      ref: 377f0a6a2d0aa1202edef638508ddb708f658bf5
  paytm_allinonesdk:
    git:
      url: https://github.com/inspireui/paytm_allinonesdk.git
      ref: 5a1e27bbf583388fb14470f90f078ed962b0c444
  sms_autofill:
    git:
      url: https://github.com/inspireui/sms_autofill.git # Fix issue to compatible with Flutter 3.29
      ref: 50faf1ed6e5bfdd847803ef8b7456d9a10163f89
  # Outdated dependency webview_flutter: 4.7.0
  pay_with_paystack:
    git:
      url: https://github.com/inspireui/pay_with_paystack
      ref: 34163d42305f92e4071ab2fcf6fc8c16429397f5
  # This package is discontinued
  flutter_paystack:
    git:
      url: https://github.com/inspireui/flutter_paystack.git
      ref: d5355aa84e0884730e259d6c46f5e5dcf46e658a
  # This package is discontinued
  flutterwave_standard:
    git:
      url: https://github.com/inspireui/Flutterwave-v3.git
      ref: 98ddce93dd267962ac16d05a2ad8b784e6241963

  # Customize to adapt with the project
  flutter_async_autocomplete:
    git:
      url: https://github.com/inspireui/flutter_async_autocomplete.git
      ref: e4099b4ae69b9a02f174ca0e4db6b1d2c356a924

  ### Flutter 3 fix warning
  intl_phone_number_input: # custom feature for phone number
    git:
      url: https://github.com/inspireui/intl_phone_number_input
      ref: 7408d78d555b0516feb051f58d7053caff082cd0
  # This package is discontinued
  flutter_swiper_null_safety:
    git:
      url: https://github.com/inspireui/flutter_swiper_null_safety
      ref: 18e8d2d642ff9e0013fc4477c6d64c52732b463a

  # This package is discontinued
  flare_flutter:
    git:
      url: https://github.com/inspireui/Flare-Flutter.git
      ref: 0211568820af2ba96fedb7c51462ec5a6bf566b9
      path: flare_flutter
  # This package is discontinued
  pull_to_refresh:
    git:
      url: https://github.com/inspireui/flutter_pull_to_refresh
      ref: 24df296e282cd8b4ea8d3a5e171f4156a0295d1b
  shah_widgets:
    git:
      url: https://github.com/inspireui/shah_widgets
      ref: 016cd3323eaaf0acbf63ad3de8f45729cd12e438
  flutter_native_image:
    git:
      url: https://github.com/inspireui/flutter_native_image.git # Fix issue to compatible with Flutter 3.29
      ref: 1d68ebd2df3050d71ee4b28f6daedecdace5af55

  onesignal_flutter:
    git:
      url: https://github.com/inspireui/OneSignal-Flutter-SDK.git # Fix issue to compatible with Flutter 3.29
      ref: 1b728a59a5dbe42474f1179edbdbebb5a259c9d4

  # Currently, there is an issue that automatically changes the bottom
  # navigation bar or status bar according to the background.
  # Issue: https://github.com/sososdk/flash/issues/74
  flash:
    git:
      url: https://github.com/inspireui/flash
      ref: 85b7213d94f9f590e8d900593c2275f1456f0e37

  # Fix issue to compatible with Flutter 3.32 
  flutter_calendar_carousel:
    git:
      url: https://github.com/inspireui/flutter_calendar_carousel.git
      ref: badfb7f1660c121fb5f370cd3c0fff1fdc3e2579

  ###---- Some extra feature is disable by default -----###
  ### 💳 Facebook Ads
  ### Search "Enable Facebook Ads" & uncomment to use  - https://tppr.me/9Pkf9
  # facebook_audience_network: 1.0.1

  ### ⬇️ Enable In App Update on Android feature
  ### Uncomment file lib/common/tools/in_app_update_for_android.dart
  in_app_update: 4.2.3
  meta_seo: 3.0.9

  #### For Shopify GraphQL
  graphql: 5.2.0-beta.11

dependency_overrides:
  ### Fixed issue(workaround): Crash when opening webview on iOS
  ##  Related: https://github.com/flutter/flutter/issues/162437
  webview_flutter_wkwebview: 3.17.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.4.0
  build_runner: ^2.4.10
  freezed: ^2.5.2
  json_serializable: ^6.8.0
  injectable_generator: 2.6.1

  ### Enable to use Flutter Test Driver
  #  flutter_driver:
  #    sdk: flutter
  #  test: 1.16.5
  #  dependency_validator: 3.2.2  # pub run dependency_validator

  ### Enable Generate Icon library
  flutter_launcher_icons: 0.13.1

#  Run this script to generate the app icon: flutter pub run flutter_launcher_icons:main

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.png"

flutter:
  uses-material-design: true
  assets:
    - lib/config/
    - lib/config/states/
    - lib/config/stories/
    - lib/config/us_store/
    - lib/config/vi_store/
    - assets/icons/brands/
    - assets/icons/credit_cards/
    - assets/icons/tabs/
    - assets/icons/payment/
    - assets/icons/logins/
    - assets/icons/loyalty/
    - assets/images/
    - assets/images/country/
    - assets/map_styles/
    - assets/html/
    - google_fonts/

### Uncomment to use custom font
#  fonts:
#    - family: Your Custom Font
#      fonts:
#      - asset: google_fonts/GE-Hili-Book.ttf

# flutter_intl:
#   enabled: false
#   use_deferred_loading: true
