name: flux_firebase
description: A Firebase package for FluxStore apps
publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ">=3.3.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  fstore:
    path: ../../
    
  inspireui: 2.3.0
  flux_ui: 0.0.8

  flux_interface:
    path: ../flux_interface

  flux_localization:
    path: ../flux_localization
    
  # FIREBASE PACKAGES
  firebase_core: 3.13.1
  firebase_analytics: 11.4.6
  firebase_auth: 5.5.4
  firebase_remote_config: 5.4.4
  firebase_dynamic_links: 6.1.6
  cloud_firestore: 5.6.8
  cloud_functions: 5.5.1
  firebase_remote_config_web: 1.8.4

  # Additional Firebase packages. To prevent unexpected upgrade.
  firebase_core_platform_interface: 5.4.0
  firebase_analytics_platform_interface: 4.3.6
  firebase_auth_platform_interface: 7.6.3
  firebase_remote_config_platform_interface: 1.5.4
  firebase_dynamic_links_platform_interface: 0.2.7+6
  cloud_firestore_platform_interface: 6.6.8
  firebase_messaging_platform_interface: 4.6.6

  # PUSH NOTIFICATION
  firebase_messaging: 15.2.6

  # OTHERS
  intl: any
  timeago: 3.7.1
  # flutter_local_notifications: 18.0.1
  share_plus: 10.1.4
  provider: 6.1.2
  collection: any
  easy_debounce: 2.0.3
  jumping_dot: 0.0.7
  flutter_linkify: 6.0.0
  flutter_facebook_auth_platform_interface: ^6.1.2

flutter:
  uses-material-design: true